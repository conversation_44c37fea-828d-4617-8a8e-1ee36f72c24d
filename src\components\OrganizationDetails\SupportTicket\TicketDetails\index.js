'use client';
import { Box, Typography } from '@mui/material';
import PersonIcon from '@mui/icons-material/Person';
import AccessTimeIcon from '@mui/icons-material/AccessTime';
import React, { useState } from 'react';
import {
  DateFormat,
  checkOrganizationRole,
} from '@/helper/common/commonFunctions';
import CustomTabs from '@/components/UI/CustomTabs';
import Conversation from './Conversation';
// TODO: Uncomment TimeEntry import when needed in future
// import TimeEntry from './TimeEntry';
import Attachment from './Attachment';
import TicketInformation from './TicketInformation';
import History from './History';
import ArrowBackIosIcon from '@mui/icons-material/ArrowBackIos';
import './ticketdetails.scss';

export default function TicketDetails({
  ticket,
  onMobileBackClick,
  isMobileView = false,
  onTicketStatusChange,
}) {
  // Get user role to determine access
  const isStaff = checkOrganizationRole('staff');
  const isSuperAdmin = checkOrganizationRole('super_admin');

  // Set initial tab based on user role
  const getInitialTab = () => {
    // if (isStaff) {
    //   return 2; // Conversation tab for staff users
    // }
    return 1; // Ticket Information tab for other users
  };

  const [activeTab, setActiveTab] = useState(getInitialTab());

  // Filter tabs based on user role
  const getFilteredTabs = () => {
    const allTabs = [
      { id: 1, label: 'Ticket Information' },
      { id: 2, label: 'Conversation' },
      // TODO: Uncomment Time Entry when needed in future
      // { id: 3, label: 'Time Entry' },
      { id: 4, label: 'Attachment' },
      { id: 5, label: 'History' },
    ];

    let filteredTabs = allTabs;

    // Staff users cannot see Ticket Information tab
    // if (isStaff) {
    //   filteredTabs = filteredTabs?.filter((tab) => tab?.id !== 1);
    // }

    // Only super_admin can see History tab
    if (!isSuperAdmin) {
      filteredTabs = filteredTabs?.filter((tab) => tab?.id !== 5);
    }

    return filteredTabs;
  };

  const reports_tabs = getFilteredTabs();

  const handleTabChange = (newValue) => {
    setActiveTab(newValue);
  };

  const getCurrentContent = () => {
    switch (activeTab) {
      case 1:
        return (
          <TicketInformation
            ticket={ticket}
            onTicketStatusChange={onTicketStatusChange}
          />
        );
      case 2:
        return <Conversation ticketId={ticket?.id} ticket={ticket} />;
      // TODO: Uncomment Time Entry when needed in future
      // case 3:
      //   return <TimeEntry />;
      case 4:
        return <Attachment attachments={ticket?.attachments || []} />;
      case 5:
        return <History ticketId={ticket?.id} />;
      default:
        // For staff users, default to Conversation tab since Ticket Information is hidden
        // For non-super_admin users, default to Conversation if History is not available
        if (isStaff) {
          return <Conversation ticketId={ticket?.id} ticket={ticket} />;
        } else if (!isSuperAdmin && activeTab === 5) {
          // If user is not super_admin and tries to access History tab, redirect to Ticket Information
          return <TicketInformation ticket={ticket} />;
        } else {
          return <TicketInformation ticket={ticket} />;
        }
    }
  };

  return (
    <Box className="details-wrap">
      <Box className="ticket-details-wrap">
        <Box className="d-flex align-center gap-sm mb8">
          <Box className="d-flex align-center">
            {isMobileView && onMobileBackClick && (
              <ArrowBackIosIcon
                className="cursor-pointer mobile-back-button"
                onClick={onMobileBackClick}
              />
            )}
            <Typography className="id-wrap body-text fw600">
              #{ticket?.id}
            </Typography>
          </Box>
          <Typography className="body-text fw600">
            {ticket?.ticket_title}
          </Typography>
        </Box>
        <Box className="detail-wrap d-flex align-center gap-10 flex-wrap">
          <Box className="d-flex align-center gap-5 user-info-section">
            <PersonIcon className="user-icon" />
            <Box className="d-flex align-center">
              <Typography className="body-sm pr4 text-capital">
                {ticket?.creator_full_name}
              </Typography>
              {ticket?.organizationName && (
                <>
                  <Typography className="from-text body-sm pr4">
                    from
                  </Typography>
                  <Typography className="body-sm">
                    {ticket?.organizationName}
                  </Typography>
                </>
              )}
            </Box>
          </Box>

          <Box className="d-flex align-center gap-5 time-info-section">
            <AccessTimeIcon className="time-icon" />
            <Typography className="date-time-text body-sm">
              Created on:{' '}
              {ticket?.created_at &&
                DateFormat(ticket?.created_at, 'datesWithhour')}
            </Typography>
          </Box>
          <Box className="d-flex align-center gap-5 time-info-section">
            <AccessTimeIcon className="time-icon" />
            <Typography className="date-time-text body-sm">
              Updated at:{' '}
              {ticket?.updated_at &&
                DateFormat(ticket?.updated_at, 'datesWithhour')}
            </Typography>
          </Box>
          {/* #TODO: Currently not in use if needed then uncomment */}
          {/* <Box className="d-flex align-center">
            <QueryBuilderIcon className="timer-icon" />
            <Typography className="time-wrap body-sm">Time</Typography>
          </Box>
          <Box className="watch-wrap">
            <Stopwatch
              time={time}
              setTime={setTime}
              running={running}
              setRunning={setRunning}
              savedTimes={savedTimes}
              setSavedTimes={setSavedTimes}
              showButtons={showButtons}
              setShowButtons={setShowButtons}
            />
          </Box> */}
        </Box>
      </Box>
      <Box className="ticket-tab-handler">
        <CustomTabs
          tabs={reports_tabs}
          initialTab={activeTab}
          onTabChange={handleTabChange}
        />
        <Box>{getCurrentContent()}</Box>
      </Box>
    </Box>
  );
}
