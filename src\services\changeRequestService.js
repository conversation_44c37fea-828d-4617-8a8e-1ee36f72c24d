import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import dayjs from 'dayjs';

export const changeRequestService = {
  getChangeRequestList: async (
    search = '',
    pageNo = 1,
    statusValue = '',
    date = '',
    rowsPerPage = 10,
    startDate = '',
    endDate = ''
  ) => {
    try {
      const datef = date ? dayjs(date)?.format('YYYY-MM-DD') : '';
      const startDatef = startDate
        ? dayjs(startDate)?.format('YYYY-MM-DD')
        : '';
      const endDatef = endDate ? dayjs(endDate)?.format('YYYY-MM-DD') : '';

      let url =
        URLS?.GET_CR_LIST +
        `?search=${search}&page=${pageNo}&size=${rowsPerPage}&change_request_status=${statusValue}`;

      // Add date parameters based on what's provided
      if (startDatef && endDatef) {
        // Date range filtering
        url += `&start_date=${startDatef}&end_date=${endDatef}`;
      } else if (datef) {
        // Single date filtering (existing behavior)
        url += `&change_request_date=${datef}`;
      }

      const { status, data } = await axiosInstance.get(url);
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  getOwnChangeRequestList: async (
    search = '',
    pageNo = 1,
    statusValue = '',
    date = '',
    rowsPerPage = 10,
    startDate = '',
    endDate = ''
  ) => {
    try {
      const datef = date ? dayjs(date)?.format('YYYY-MM-DD') : '';
      const startDatef = startDate
        ? dayjs(startDate)?.format('YYYY-MM-DD')
        : '';
      const endDatef = endDate ? dayjs(endDate)?.format('YYYY-MM-DD') : '';

      let url =
        URLS?.GET_OWN_CR_LIST +
        `?search=${search}&page=${pageNo}&size=${rowsPerPage}&change_request_status=${statusValue}`;

      // Add date parameters based on what's provided
      if (startDatef && endDatef) {
        // Date range filtering
        url += `&start_date=${startDatef}&end_date=${endDatef}`;
      } else if (datef) {
        // Single date filtering (existing behavior)
        url += `&change_request_date=${datef}`;
      }

      const { status, data } = await axiosInstance.get(url);
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  deleteChangeRequest: async (id) => {
    try {
      const { status, data } = await axiosInstance.delete(
        URLS?.DELETE_CHANGE_REQUEST + id
      );
      if (status === 200) {
        return data;
      }
      return null;
    } catch (error) {
      throw error;
    }
  },

  // Get change request by ID
  getCRById: async (crId) => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_CR_BY_ID + `${crId}`
      );
      return { status, data };
    } catch (error) {
      throw error;
    }
  },

  // Approve/Reject change request
  updateCRStatus: async (crId, requestData) => {
    try {
      const { status, data } = await axiosInstance.post(
        URLS?.APPROVE_REJECT_CR + `${crId}`,
        requestData
      );
      return { status, data };
    } catch (error) {
      throw error;
    }
  },

  // Get available fields for change request configuration
  getCRFields: async () => {
    try {
      const { status, data } = await axiosInstance.get(URLS?.GET_CR_FIELDS);
      if (status === 200) {
        return { success: true, data: data?.data };
      }
      return { success: false, data: null };
    } catch (error) {
      throw error;
    }
  },

  // Get stored field configuration
  getStoredCRFields: async () => {
    try {
      const { status, data } = await axiosInstance.get(
        URLS?.GET_STORED_CR_FIELDS
      );
      if (status === 200) {
        return { success: true, data: data?.data };
      }
      return { success: false, data: null };
    } catch (error) {
      throw error;
    }
  },

  // Store field configuration
  storeCRFields: async (fieldOrder) => {
    try {
      const { status, data } = await axiosInstance.post(URLS?.STORE_CR_FIELDS, {
        user_field_order: fieldOrder,
      });
      if (status === 200) {
        return { success: true, data };
      }
      return { success: false, data };
    } catch (error) {
      throw error;
    }
  },
};

export const addChangeRequest = async (requestData, files) => {
  const body = new FormData();

  if (requestData?.subject) {
    body.append('change_request_subject', requestData.subject);
  }
  if (requestData?.old_value) {
    body.append('old_data', requestData.old_value);
  }
  if (requestData?.new_value) {
    body.append('new_data', requestData.new_value);
  }

  if (files && files.length > 0) {
    files.forEach((file) => {
      body.append('change_request_files', file);
    });
  }

  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };

  const response = await axiosInstance.post(URLS.ADD_CR, body, config);
  return response;
};

export const updateChangeRequest = async (requestData, files, crId) => {
  const body = new FormData();

  requestData?.subject &&
    body.append('change_request_subject', requestData?.subject);
  requestData?.old_value && body.append('old_data', requestData?.old_value);
  requestData?.new_value && body.append('new_data', requestData?.new_value);

  files &&
    files?.length > 0 &&
    files.map((file) => {
      body.append(`change_request_files`, file);
    });

  const config = {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  };

  const response = await axiosInstance.post(
    URLS.ADD_CR + `/${crId}`,
    body,
    config
  );

  return response;
};

export const getChangeRequestById = async (crId) => {
  try {
    const { status, data } = await axiosInstance.get(
      URLS?.GET_CR_BY_ID + `${crId}`
    );
    if (status === 200) {
      return { success: true, data: data?.data };
    }
    return { success: false, data: null };
  } catch (error) {
    return {
      success: false,
      data: null,
      error: error?.response?.data?.message,
    };
  }
};
