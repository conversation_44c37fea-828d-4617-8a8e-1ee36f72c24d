'use client';
import React, { useEffect, useState } from 'react';
import { InputAdornment, Box } from '@mui/material';
import SearchIcon from '@mui/icons-material/Search';
import ClearIcon from '@mui/icons-material/Clear';
import CustomTextField from '@/components/UI/CustomTextField';

const CustomSearch = ({
  searchclass,
  setSearchValue,
  searchValue,
  onKeyPress,
  isClearSearch,
  handleClearSearch,
  label = '',
}) => {
  const [search, setSearch] = useState('');

  // Function to remove special characters
  const removeSpecialChars = (str) => {
    // Allow alphanumeric characters, spaces, basic punctuation and @ symbol
    return str?.replace(/[^a-zA-Z0-9\s.,!?@-]/g, '');
  };

  const handleSearchChange = (e) => {
    const newValue = e.target.value;
    setSearch(newValue);
    setSearchValue(newValue);
  };

  const handleInput = (e) => {
    const input = e.target;
    const value = input.value;
    const filteredValue = removeSpecialChars(value);

    // If the value contains special characters, update the input
    if (value !== filteredValue) {
      input.value = filteredValue;
      setSearch(filteredValue);
      setSearchValue(filteredValue);
    }
  };

  useEffect(() => {
    if (searchValue || searchValue === '') {
      setSearch(searchValue);
    }
  }, [searchValue]);

  return (
    <Box className="org-custom-search-bar-sec">
      <CustomTextField
        label={label}
        className={searchclass}
        fullWidth
        placeholder="Search"
        value={search}
        onChange={(e) => setSearch(e.target.value)}
        onInput={handleInput}
        onBlur={(e) => handleSearchChange(e)}
        onKeyDown={(e) => {
          if (e.key === 'Enter') {
            handleSearchChange(e);
          }
        }}
        onKeyPress={onKeyPress}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon />
            </InputAdornment>
          ),
          endAdornment: search && (
            <InputAdornment position="end">
              <ClearIcon
                onClick={() => {
                  if (isClearSearch) {
                    setSearch('');
                    setSearchValue('');
                    handleClearSearch();
                  } else {
                    setSearch('');
                    setSearchValue('');
                  }
                }}
                style={{ cursor: 'pointer' }}
              />
            </InputAdornment>
          ),
        }}
      />
    </Box>
  );
};

export default CustomSearch;
