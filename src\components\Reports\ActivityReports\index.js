'use client';
import React, { useState, useEffect, useContext, useCallback } from 'react';
import {
  Box,
  Typography,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import ExpandMoreIcon from '@mui/icons-material/ExpandMore';
import FilterListIcon from '@mui/icons-material/FilterList';
import CustomTextField from '@/components/UI/CustomTextField';
import CustomSelect from '@/components/UI/CustomSelect';
import CustomDateRangePicker from '@/components/UI/CustomDateRangePicker';
import CustomButton from '@/components/UI/CustomButton';
import CommonTable from '@/components/UI/CommonTable/CommonTable';
import NoDataView from '@/components/UI/NoDataView';
import DialogBox from '@/components/UI/Modalbox';
import CommonUserDetails from '@/components/UI/CommonUserDetails';
import ContentLoader from '@/components/UI/ContentLoader';
import AuthContext from '@/helper/authcontext';
import axiosInstance from '@/helper/axios/axiosInstance';
import { URLS } from '@/helper/constants/urls';
import { setApiMessage, DateFormat } from '@/helper/common/commonFunctions';
import Icon from '@/components/UI/AppIcon/AppIcon';
import dayjs from 'dayjs';
import '@/components/UI/FilterCollapse/FilterCollapse.scss';

// Custom Filter Component for Activity Reports
const ActivityReportsFilter = ({ onApply, initialValues = {} }) => {
  const [expanded, setExpanded] = useState(false);
  const [values, setValues] = useState(initialValues);

  const timePeriodOptions = [
    { label: 'Week', value: 'week' },
    { label: 'Month', value: 'month' },
    { label: 'Custom', value: 'custom' },
  ];

  const handleFieldChange = (name, value) => {
    setValues((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleApply = () => {
    onApply(values);
  };

  const handleClear = () => {
    setValues({});
    onApply({});
  };

  return (
    <div className="filter-collapse-container">
      <Accordion
        expanded={expanded}
        onChange={() => setExpanded((prev) => !prev)}
        className="filter-collapse-accordion"
        elevation={0}
      >
        <AccordionSummary
          className="filter-collapse-header"
          expandIcon={<ExpandMoreIcon />}
        >
          <span className="filter-collapse-icon">
            <FilterListIcon />
          </span>
          <span className="filter-collapse-title">Filters</span>
        </AccordionSummary>
        <AccordionDetails className="filter-collapse-accordion-details">
          <div className="filter-collapse-fields">
            <div className="filter-fields-row">
              {/* Search Field */}
              <div className="filter-field">
                <CustomTextField
                  fullWidth
                  label="Search"
                  placeholder="Search"
                  value={values.search || ''}
                  onChange={(e) => handleFieldChange('search', e.target.value)}
                  name="search"
                />
              </div>

              {/* Time Period Field */}
              <div className="filter-field">
                <CustomSelect
                  label="Time Period"
                  placeholder="Select Time Period"
                  options={timePeriodOptions}
                  value={
                    timePeriodOptions.find(
                      (opt) => opt.value === values.timePeriod
                    ) || ''
                  }
                  onChange={(opt) =>
                    handleFieldChange('timePeriod', opt?.value)
                  }
                  name="timePeriod"
                />
              </div>

              {/* Conditional Date Range Field - Only show when Custom is selected */}
              {values.timePeriod === 'custom' && (
                <div className="filter-field">
                  <CustomDateRangePicker
                    label="Date Range"
                    value={values.dateRange || [null, null]}
                    onChange={(range) => handleFieldChange('dateRange', range)}
                    placeholder="Select date range"
                    format="MMM dd, yyyy"
                  />
                </div>
              )}
            </div>

            <div className="filter-collapse-actions">
              <CustomButton
                variant="contained"
                onClick={handleApply}
                title="Apply Filters"
              />
              <CustomButton
                variant="outlined"
                onClick={handleClear}
                title="Clear"
              />
            </div>
          </div>
        </AccordionDetails>
      </Accordion>
    </div>
  );
};

export default function ActivityReports() {
  const { authState, setUserdata } = useContext(AuthContext);

  const [activityList, setActivityList] = useState([]);
  const [loader, setLoader] = useState(false);
  const [filters, setFilters] = useState({});
  const [page, setPage] = useState(1);
  const [totalCount, setTotalCount] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [searchValue, setSearchValue] = useState('');
  const [toggleModal, setToggleModal] = useState(false);
  const [userAgentValue, setUserAgentValue] = useState('');

  // Menu items for action dropdown
  const actionMenuItems = [
    {
      label: 'User Agent',
      icon: <Icon name="Eye" size={16} />,
      onClick: (_, rowData) => {
        setToggleModal(!toggleModal);
        setUserAgentValue({
          userAgent: rowData?.userAgent,
        });
      },
    },
  ];

  // CommonTable columns (keeping same structure)
  const columns = [
    {
      header: 'ID',
      accessor: 'user_id',
      sortable: false,
    },
    {
      header: 'Name',
      accessor: 'user_full_name',
      sortable: false,
      renderCell: (_, row) => (
        <CommonUserDetails
          userData={row?.users}
          searchValue={searchValue}
          page={page}
          rowsPerPage={rowsPerPage}
          authState={authState}
          setUserdata={setUserdata}
        />
      ),
    },
    {
      header: 'Date & Time',
      accessor: 'createdAt',
      sortable: false,
      renderCell: (value) => DateFormat(value, 'datesWithhour'),
    },
    {
      header: 'Activity actions',
      accessor: 'activity_action',
      sortable: false,
      renderCell: (_, row) =>
        `${row?.activity_table ? row?.activity_table : ''} ${row?.activity_action ? row?.activity_action : ''}`.trim(),
    },
    {
      header: 'IP',
      accessor: 'ip_address',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Location',
      accessor: 'location',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    {
      header: 'Address',
      accessor: 'address',
      sortable: false,
      renderCell: (value) => (value ? value : '-'),
    },
    // User agent column is now handled by CommonTable's actionMenuItems prop
  ];

  // Get activity details from API (same as Activity Logs)
  const getActivityDetails = useCallback(
    async (
      search = '',
      pageNo = 1,
      Rpp = rowsPerPage,
      startDate = '',
      endDate = ''
    ) => {
      setLoader(true);
      try {
        // Build query parameters
        let queryParams = `?search=${search}&page=${pageNo}&size=${Rpp}`;
        if (startDate && endDate) {
          queryParams += `&start_date=${startDate}&end_date=${endDate}`;
        }

        const { status, data } = await axiosInstance.get(
          URLS?.ACTIVITY_LOGS + queryParams
        );

        if (status === 200) {
          const activityData =
            data?.data &&
            data?.data?.length > 0 &&
            data?.data?.map((a, index) => {
              const newdata = JSON.parse(a?.new_data);
              return {
                ...a,
                user_id: a?.users?.id,
                id: index,
                user_full_name: a?.users?.user_full_name,
                user_email: a?.users?.user_email,
                branch_name: newdata?.branch_name ? newdata?.branch_name : '-',
                ip_address: a?.ip_address ? a?.ip_address : '-',
                new_data: newdata,
                department_name: newdata?.department_name
                  ? newdata?.department_name
                  : '-',
              };
            });
          setPage(data?.page);
          setTotalCount(data?.count);
          setActivityList(activityData ? activityData : []);
          setLoader(false);
        }
      } catch (error) {
        setLoader(false);
        setActivityList([]);
        setApiMessage('error', error?.response?.data?.message);
      }
    },
    [rowsPerPage]
  );

  // Calculate date range based on time period
  const getDateRangeForPeriod = (timePeriod) => {
    const now = dayjs();
    switch (timePeriod) {
      case 'week':
        return {
          startDate: now.startOf('week').format('YYYY-MM-DD'),
          endDate: now.endOf('week').format('YYYY-MM-DD'),
        };
      case 'month':
        return {
          startDate: now.startOf('month').format('YYYY-MM-DD'),
          endDate: now.endOf('month').format('YYYY-MM-DD'),
        };
      default:
        return { startDate: '', endDate: '' };
    }
  };

  // Handle filter apply
  const handleApplyFilters = (values) => {
    setFilters(values);
    setSearchValue(values.search || '');
    const searchTerm = values.search || '';

    let startDate = '';
    let endDate = '';

    // Handle time period and date range
    if (values.timePeriod === 'custom' && values.dateRange) {
      // For custom date range, use start and end dates
      startDate = values.dateRange?.[0]
        ? new Date(values.dateRange[0]).toISOString().split('T')[0]
        : '';
      endDate = values.dateRange?.[1]
        ? new Date(values.dateRange[1]).toISOString().split('T')[0]
        : '';
    } else if (values.timePeriod && values.timePeriod !== 'custom') {
      // For week/month, use the calculated date range
      const dateRange = getDateRangeForPeriod(values.timePeriod);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    setPage(1);
    getActivityDetails(searchTerm, 1, rowsPerPage, startDate, endDate);
  };

  // Helper function to extract date range from current filters
  const getDateRangeFromFilters = (filters) => {
    let startDate = '';
    let endDate = '';

    if (filters.timePeriod === 'custom' && filters.dateRange) {
      startDate = filters.dateRange?.[0]
        ? new Date(filters.dateRange[0]).toISOString().split('T')[0]
        : '';
      endDate = filters.dateRange?.[1]
        ? new Date(filters.dateRange[1]).toISOString().split('T')[0]
        : '';
    } else if (filters.timePeriod && filters.timePeriod !== 'custom') {
      const dateRange = getDateRangeForPeriod(filters.timePeriod);
      startDate = dateRange.startDate;
      endDate = dateRange.endDate;
    }

    return { startDate, endDate };
  };

  // Handle pagination (Rota Reports style)
  const handlePageChange = (newPage) => {
    setPage(newPage);
    const searchTerm = filters.search || '';
    const { startDate, endDate } = getDateRangeFromFilters(filters);
    getActivityDetails(searchTerm, newPage, rowsPerPage, startDate, endDate);
  };

  const handleRowsPerPageChange = (newRowsPerPage) => {
    setRowsPerPage(newRowsPerPage);
    setPage(1);
    const searchTerm = filters.search || '';
    const { startDate, endDate } = getDateRangeFromFilters(filters);
    getActivityDetails(searchTerm, 1, newRowsPerPage, startDate, endDate);
  };

  // Initial load
  useEffect(() => {
    getActivityDetails();
  }, [getActivityDetails]);

  return (
    <>
      <Box className="report-main-container">
        <ActivityReportsFilter
          onApply={handleApplyFilters}
          initialValues={filters}
        />

        <Box className="report-table-container">
          {loader ? (
            <ContentLoader />
          ) : activityList && activityList?.length === 0 ? (
            <NoDataView
              title="No Activity Records Found"
              description="There is no Activity data available at the moment."
            />
          ) : (
            <CommonTable
              columns={columns}
              data={activityList}
              pageSize={rowsPerPage}
              currentPage={page}
              totalCount={totalCount}
              onPageChange={handlePageChange}
              onRowsPerPageChange={handleRowsPerPageChange}
              actionMenuItems={actionMenuItems}
            />
          )}
        </Box>
      </Box>
      <DialogBox
        open={toggleModal}
        handleClose={() => {
          setToggleModal(!toggleModal);
          setUserAgentValue('');
        }}
        title="User Agent"
        className="dialog-box-container"
        content={
          <Box>
            <Typography>{userAgentValue?.userAgent}</Typography>
          </Box>
        }
      />
    </>
  );
}
